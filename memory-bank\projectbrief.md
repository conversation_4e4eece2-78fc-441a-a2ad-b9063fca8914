# MapboxGL Better - 项目简介

## 项目定位
**名称**: `mapboxgl-better`  
**目标**: 用 Vue3 + Vite + TypeScript 复刻并超越 maps.suunto.com 的核心能力，实现**轻量、高性能、易扩展**的地图演示框架。

## 核心目标
1. **1:1 复刻** maps.suunto.com 的所有核心功能
2. **技术升级** 使用现代 Vue3 生态替代 React 技术栈
3. **性能优化** 实现更快的构建速度和运行性能
4. **易于扩展** 提供清晰的架构和组件化设计

## 技术栈选型

| 原项目技术 | Vue3 替代方案 | 迁移理由 |
|------------|---------------|----------|
| React 18   | **Vue3** (`<script setup>` + 组合式 API) | 更轻量，天然类型支持 |
| React Router | **Vue Router 4** | 文件路由 + 动态路由 |
| MUI        | **Naive UI** | 更小体积，按需加载 |
| React Context | **Pinia** (状态管理) | 类型安全，模块化 |
| React Hooks | **Vue 组合式函数** | 逻辑复用更直观 |
| Webpack    | **Vite** (ESBuild + Rollup) | 启动快 10-20 倍 |

## 核心功能要求
- **3D 地图可视化**: 基于 Mapbox GL JS 的高性能 3D 地图
- **GPS 轨迹动画**: 平滑的轨迹播放和相机跟随
- **地形集成**: 智能地形数据源选择和夸张度调整
- **WebGL 渲染**: 自定义着色器实现高性能轨迹渲染
- **多语言支持**: 中英文双语界面
- **响应式设计**: 支持桌面和移动设备

## 成功标准
- **性能目标**: 60FPS 流畅动画，支持 10000+ 轨迹点
- **视觉效果**: 达到 maps.suunto.com 的视觉质量
- **功能完整性**: 100% 复刻核心功能
- **代码质量**: TypeScript 完整类型支持，90%+ 测试覆盖率

## 项目价值
- **学习价值**: 深度理解世界级 3D 地图应用的技术实现
- **技术价值**: 探索 Vue3 在复杂 3D 应用中的最佳实践
- **实用价值**: 提供可复用的高性能地图组件库
