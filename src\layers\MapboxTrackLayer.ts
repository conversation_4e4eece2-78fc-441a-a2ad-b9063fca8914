import type { Map } from 'mapbox-gl'
import type { TrackPoint } from '@/types'

/**
 * 基于 Mapbox GL JS 原生图层的轨迹显示
 * 使用 line 和 circle 图层实现轨迹可视化
 */
export class MapboxTrackLayer {
  private map: Map
  private trackPoints: TrackPoint[] = []
  private layerId: string
  private sourceId: string
  private currentProgress: number = 0

  constructor(map: Map, layerId: string = 'track-layer') {
    this.map = map
    this.layerId = layerId
    this.sourceId = `${layerId}-source`
  }

  /**
   * 设置轨迹数据
   */
  setTrackPoints(trackPoints: TrackPoint[]): void {
    this.trackPoints = trackPoints
    this.createTrackSource()
    this.createTrackLayers()
  }

  /**
   * 创建轨迹数据源
   */
  private createTrackSource(): void {
    // 转换为 GeoJSON LineString
    const coordinates = this.trackPoints.map(point => [point.lng, point.lat])
    
    const geojson = {
      type: 'Feature' as const,
      properties: {},
      geometry: {
        type: 'LineString' as const,
        coordinates
      }
    }

    // 移除已存在的数据源
    if (this.map.getSource(this.sourceId)) {
      this.map.removeSource(this.sourceId)
    }

    // 添加新的数据源
    this.map.addSource(this.sourceId, {
      type: 'geojson',
      data: geojson
    })

    console.log('轨迹数据源创建完成:', {
      points: this.trackPoints.length,
      coordinates: coordinates.length,
      sourceId: this.sourceId
    })
  }

  /**
   * 创建轨迹图层
   */
  private createTrackLayers(): void {
    // 移除已存在的图层
    this.removeTrackLayers()

    // 1. 轨迹线图层
    this.map.addLayer({
      id: `${this.layerId}-line`,
      type: 'line',
      source: this.sourceId,
      layout: {
        'line-join': 'round',
        'line-cap': 'round'
      },
      paint: {
        'line-color': '#ff0000',  // 红色轨迹线
        'line-width': 4,
        'line-opacity': 0.8
      }
    })

    // 2. 起点标记
    if (this.trackPoints.length > 0) {
      const startPoint = this.trackPoints[0]
      this.map.addSource(`${this.sourceId}-start`, {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: { type: 'start' },
          geometry: {
            type: 'Point',
            coordinates: [startPoint.lng, startPoint.lat]
          }
        }
      })

      this.map.addLayer({
        id: `${this.layerId}-start`,
        type: 'circle',
        source: `${this.sourceId}-start`,
        paint: {
          'circle-radius': 8,
          'circle-color': '#00ff00',  // 绿色起点
          'circle-stroke-width': 2,
          'circle-stroke-color': '#ffffff'
        }
      })
    }

    // 3. 终点标记
    if (this.trackPoints.length > 1) {
      const endPoint = this.trackPoints[this.trackPoints.length - 1]
      this.map.addSource(`${this.sourceId}-end`, {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: { type: 'end' },
          geometry: {
            type: 'Point',
            coordinates: [endPoint.lng, endPoint.lat]
          }
        }
      })

      this.map.addLayer({
        id: `${this.layerId}-end`,
        type: 'circle',
        source: `${this.sourceId}-end`,
        paint: {
          'circle-radius': 8,
          'circle-color': '#ff0000',  // 红色终点
          'circle-stroke-width': 2,
          'circle-stroke-color': '#ffffff'
        }
      })
    }

    console.log('轨迹图层创建完成:', {
      lineLayer: `${this.layerId}-line`,
      startLayer: `${this.layerId}-start`,
      endLayer: `${this.layerId}-end`
    })
  }

  /**
   * 更新轨迹进度（用于动画播放）
   */
  updateProgress(progress: number): void {
    this.currentProgress = Math.max(0, Math.min(1, progress))
    
    if (this.trackPoints.length === 0) return

    // 计算当前进度对应的点数
    const currentPointIndex = Math.floor(this.currentProgress * (this.trackPoints.length - 1))
    const visiblePoints = this.trackPoints.slice(0, currentPointIndex + 1)

    if (visiblePoints.length < 2) return

    // 更新轨迹线数据源
    const coordinates = visiblePoints.map(point => [point.lng, point.lat])
    const geojson = {
      type: 'Feature' as const,
      properties: {},
      geometry: {
        type: 'LineString' as const,
        coordinates
      }
    }

    const source = this.map.getSource(this.sourceId) as mapboxgl.GeoJSONSource
    if (source) {
      source.setData(geojson)
    }

    // 添加当前位置标记
    this.updateCurrentPosition(visiblePoints[visiblePoints.length - 1])
  }

  /**
   * 更新当前位置标记
   */
  private updateCurrentPosition(currentPoint: TrackPoint): void {
    const currentSourceId = `${this.sourceId}-current`
    
    // 更新当前位置数据源
    if (this.map.getSource(currentSourceId)) {
      const source = this.map.getSource(currentSourceId) as mapboxgl.GeoJSONSource
      source.setData({
        type: 'Feature',
        properties: { type: 'current' },
        geometry: {
          type: 'Point',
          coordinates: [currentPoint.lng, currentPoint.lat]
        }
      })
    } else {
      // 创建当前位置数据源和图层
      this.map.addSource(currentSourceId, {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: { type: 'current' },
          geometry: {
            type: 'Point',
            coordinates: [currentPoint.lng, currentPoint.lat]
          }
        }
      })

      this.map.addLayer({
        id: `${this.layerId}-current`,
        type: 'circle',
        source: currentSourceId,
        paint: {
          'circle-radius': 6,
          'circle-color': '#0080ff',  // 蓝色当前位置
          'circle-stroke-width': 2,
          'circle-stroke-color': '#ffffff'
        }
      })
    }
  }

  /**
   * 移除轨迹图层
   */
  private removeTrackLayers(): void {
    const layerIds = [
      `${this.layerId}-line`,
      `${this.layerId}-start`,
      `${this.layerId}-end`,
      `${this.layerId}-current`
    ]

    layerIds.forEach(layerId => {
      if (this.map.getLayer(layerId)) {
        this.map.removeLayer(layerId)
      }
    })

    const sourceIds = [
      `${this.sourceId}-start`,
      `${this.sourceId}-end`,
      `${this.sourceId}-current`
    ]

    sourceIds.forEach(sourceId => {
      if (this.map.getSource(sourceId)) {
        this.map.removeSource(sourceId)
      }
    })
  }

  /**
   * 适配地图视图到轨迹范围
   */
  fitToTrack(): void {
    if (this.trackPoints.length === 0) return

    const bounds = new mapboxgl.LngLatBounds()
    this.trackPoints.forEach(point => {
      bounds.extend([point.lng, point.lat])
    })

    this.map.fitBounds(bounds, {
      padding: 50,
      duration: 1000
    })
  }

  /**
   * 销毁图层
   */
  destroy(): void {
    this.removeTrackLayers()
    
    if (this.map.getSource(this.sourceId)) {
      this.map.removeSource(this.sourceId)
    }
  }
}
