import { Renderer } from './Renderer'

/**
 * 点渲染器
 * 参照 maps.suunto.com 的 PointRenderer 实现
 */
export class PointRenderer extends Renderer {
  constructor(gl: WebGLRenderingContext) {
    const vertexShader = `
      precision highp float;
      
      attribute vec4 aPos;
      attribute float aDist;
      
      uniform mat4 uMatrix;
      uniform vec4 uCamera;
      uniform float uThickness;
      uniform vec2 uOffset;
      
      varying float vItem;
      varying vec2 vDelta;
      varying float vAlpha;
      varying float vDist;
      
      vec3 alt = vec3(0.0, 0.0, 0.0);
      
      void main() {
        vec3 pos = aPos.xyz;
        float item = aPos.w;
        vec2 delta;
        
        // 解析顶点类型和偏移
        if(item < 3.0) {
          item -= 2.0;
          delta = vec2(-1.0, -1.0);
        } else if(item < 4.0) {
          item -= 3.0;
          delta = vec2(-1.0, 1.0);
        } else if(item < 5.0) {
          item -= 4.0;
          delta = vec2(1.0, -1.0);
        } else {
          item -= 5.0;
          delta = vec2(1.0, 1.0);
        }
        
        vItem = item;
        vDelta = delta;
        vDist = aDist;
        
        // 高度偏移和相机补偿
        pos += alt;
        pos += normalize(uCamera.xyz - pos) * 0.000001;
        
        // 投影变换
        vec4 xyPos = uMatrix * vec4(pos, 1.0);
        
        // 距离衰减计算
        vAlpha = 1.0 - smoothstep(
          max(5.0, uCamera.w), 
          max(7.5, uCamera.w * 2.0), 
          length(uCamera.xyz - pos) / uCamera.z
        );
        
        // 点的偏移处理
        xyPos += vec4(uOffset * delta, 0.0, 0.0) * xyPos.w * uThickness;
        
        gl_Position = xyPos;
      }
    `

    const fragmentShader = `
      precision highp float;
      
      uniform float uAlpha;
      uniform float uDist;
      
      varying float vItem;
      varying vec2 vDelta;
      varying float vAlpha;
      varying float vDist;
      
      void main() {
        // 进度裁剪
        if(vDist > uDist) discard;
        
        // 圆形裁剪（创建圆形点）
        if(length(vDelta) > 1.0) discard;
        
        // 颜色计算
        vec4 color;
        if(vItem == 1.0) {
          color = vec4(1.0, 1.0, 1.0, 1.0);  // 白色：特殊标记
        } else {
          color = vec4(1.0, 1.0 - vItem, 0.0, 1.0);  // 红-黄渐变
        }
        
        // 边缘软化效果
        float edge = 1.0 - length(vDelta);
        edge = smoothstep(0.0, 0.2, edge);
        
        gl_FragColor = color * uAlpha * vAlpha * edge;
      }
    `

    super(gl, vertexShader, fragmentShader)
  }

  /**
   * 渲染点
   */
  render(
    gl: WebGLRenderingContext,
    matrix: number[],
    thickness: number,
    position: number,
    camera?: number[]
  ): void {
    const count = this.count
    if (count === 0) return

    // 设置基础渲染状态
    this.setupRenderState(gl, matrix, thickness, position, camera)

    // 启用混合
    gl.enable(gl.BLEND)
    gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA)

    // 渲染点
    gl.uniform1f(this.uAlpha, 1.0)
    gl.uniform2f(this.uOffset, 1, 1)
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, count * 6)

    // 恢复混合状态
    gl.disable(gl.BLEND)
  }
}
