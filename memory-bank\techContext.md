# 技术上下文

## 技术栈

### 核心框架
- **Vue 3.4+**: 组合式 API + `<script setup>`
- **Vite 5.0+**: 极速构建工具
- **TypeScript 5.3+**: 类型安全

### 地图和渲染
- **Mapbox GL JS 3.1+**: 3D 地图引擎
- **WebGL**: 自定义着色器渲染
- **Canvas API**: 2D 图形处理

### 状态管理和路由
- **Pinia 2.1+**: 状态管理
- **Vue Router 4.2+**: 路由管理
- **Vue I18n 9.9+**: 国际化

### UI 组件库
- **Naive UI 2.38+**: Vue3 组件库
- **@vicons/ionicons5**: 图标库

### 开发工具
- **ESLint + Prettier**: 代码规范
- **Vitest**: 单元测试
- **Playwright**: E2E 测试

## 开发环境设置

### 环境要求
- **Node.js**: >= 18.0.0
- **npm/yarn/pnpm**: 最新版本
- **现代浏览器**: 支持 WebGL 2.0

### 环境变量
```env
VITE_MAPBOX_TOKEN=your-mapbox-access-token
VITE_APP_TITLE=MapboxGL Better
VITE_DEFAULT_CENTER_LAT=39.9042
VITE_DEFAULT_CENTER_LNG=116.4074
VITE_DEFAULT_ZOOM=10
```

### 开发命令
```bash
npm run dev      # 开发模式
npm run build    # 生产构建
npm run preview  # 预览构建结果
npm run test     # 运行测试
npm run lint     # 代码检查
```

## 技术约束

### 性能约束
- **帧率**: 保持 60FPS 动画
- **内存**: 控制在 512MB 以内
- **加载时间**: 首屏 < 3 秒
- **包大小**: 压缩后 < 2MB

### 浏览器兼容性
- **Chrome**: >= 90
- **Firefox**: >= 88
- **Safari**: >= 14
- **Edge**: >= 90

### 设备支持
- **桌面**: 1920x1080 及以上
- **移动**: 375x667 及以上
- **WebGL**: 必须支持 WebGL 2.0

## 依赖管理

### 核心依赖
```json
{
  "vue": "^3.4.0",
  "vite": "^5.0.0",
  "typescript": "^5.3.0",
  "mapbox-gl": "^3.1.0",
  "pinia": "^2.1.7",
  "vue-router": "^4.2.5",
  "naive-ui": "^2.38.1",
  "vue-i18n": "^9.9.0"
}
```

### 开发依赖
```json
{
  "@vitejs/plugin-vue": "^5.0.0",
  "@vue/tsconfig": "^0.5.0",
  "eslint": "^8.0.0",
  "prettier": "^3.0.0",
  "vitest": "^1.0.0",
  "playwright": "^1.40.0"
}
```

## 工具使用模式

### 构建优化
- **代码分割**: 路由级别的懒加载
- **Tree Shaking**: 移除未使用代码
- **资源压缩**: Gzip + Brotli
- **缓存策略**: 长期缓存静态资源

### 开发体验
- **热重载**: 组件级别的热更新
- **类型检查**: 实时 TypeScript 检查
- **错误提示**: 友好的错误信息
- **调试工具**: Vue DevTools 集成

### 测试策略
- **单元测试**: 工具函数和组合式函数
- **组件测试**: Vue 组件的行为测试
- **E2E 测试**: 完整用户流程测试
- **性能测试**: WebGL 渲染性能监控

## 部署配置

### 构建配置
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    target: 'es2015',
    rollupOptions: {
      output: {
        manualChunks: {
          'mapbox': ['mapbox-gl'],
          'ui': ['naive-ui'],
          'vue-vendor': ['vue', 'vue-router', 'pinia']
        }
      }
    }
  }
})
```

### 服务器配置
- **静态文件服务**: Nginx/Apache
- **HTTPS**: 必须启用 (Mapbox 要求)
- **CORS**: 配置跨域访问
- **缓存**: 设置适当的缓存头

### CDN 优化
- **资源分发**: 使用 CDN 加速静态资源
- **地理分布**: 多地域部署
- **缓存策略**: 合理的 TTL 设置
