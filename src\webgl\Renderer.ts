/**
 * WebGL 基础渲染器类
 * 参照 maps.suunto.com 的 Renderer 实现
 */
export class Renderer {
  protected program: WebGLProgram
  protected buffer: WebGLBuffer
  protected distBuffer: WebGLBuffer
  protected uMatrix: WebGLUniformLocation
  protected uCamera: WebGLUniformLocation
  protected uThickness: WebGLUniformLocation
  protected uAlpha: WebGLUniformLocation
  protected uDist: WebGLUniformLocation
  protected uOffset: WebGLUniformLocation
  protected aPos: number
  protected aDist: number
  protected count: number = 0

  constructor(
    gl: WebGLRenderingContext,
    vertexShaderSource: string,
    fragmentShaderSource: string
  ) {
    // 创建着色器程序
    this.program = this.createProgram(gl, vertexShaderSource, fragmentShaderSource)
    
    // 创建缓冲区
    this.buffer = gl.createBuffer()!
    this.distBuffer = gl.createBuffer()!
    
    // 获取 uniform 位置
    this.uMatrix = gl.getUniformLocation(this.program, 'uMatrix')!
    this.uCamera = gl.getUniformLocation(this.program, 'uCamera')!
    this.uThickness = gl.getUniformLocation(this.program, 'uThickness')!
    this.uAlpha = gl.getUniformLocation(this.program, 'uAlpha')!
    this.uDist = gl.getUniformLocation(this.program, 'uDist')!
    this.uOffset = gl.getUniformLocation(this.program, 'uOffset')!
    
    // 获取属性位置
    this.aPos = gl.getAttribLocation(this.program, 'aPos')
    this.aDist = gl.getAttribLocation(this.program, 'aDist')
  }

  /**
   * 创建着色器程序
   */
  private createProgram(
    gl: WebGLRenderingContext,
    vertexSource: string,
    fragmentSource: string
  ): WebGLProgram {
    const vertexShader = this.createShader(gl, gl.VERTEX_SHADER, vertexSource)
    const fragmentShader = this.createShader(gl, gl.FRAGMENT_SHADER, fragmentSource)
    
    const program = gl.createProgram()!
    gl.attachShader(program, vertexShader)
    gl.attachShader(program, fragmentShader)
    gl.linkProgram(program)
    
    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      const error = gl.getProgramInfoLog(program)
      gl.deleteProgram(program)
      throw new Error(`着色器程序链接失败: ${error}`)
    }
    
    return program
  }

  /**
   * 创建着色器
   */
  private createShader(
    gl: WebGLRenderingContext,
    type: number,
    source: string
  ): WebGLShader {
    const shader = gl.createShader(type)!
    gl.shaderSource(shader, source)
    gl.compileShader(shader)
    
    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
      const error = gl.getShaderInfoLog(shader)
      gl.deleteShader(shader)
      throw new Error(`着色器编译失败: ${error}`)
    }
    
    return shader
  }

  /**
   * 更新顶点数据
   */
  update(gl: WebGLRenderingContext, data: Float32Array, count: number): void {
    this.count = count
    
    gl.bindBuffer(gl.ARRAY_BUFFER, this.buffer)
    gl.bufferData(gl.ARRAY_BUFFER, data, gl.DYNAMIC_DRAW)
  }

  /**
   * 更新距离数据
   */
  updateDist(gl: WebGLRenderingContext, data: Float32Array): void {
    gl.bindBuffer(gl.ARRAY_BUFFER, this.distBuffer)
    gl.bufferData(gl.ARRAY_BUFFER, data, gl.DYNAMIC_DRAW)
  }

  /**
   * 设置基础渲染状态
   */
  protected setupRenderState(
    gl: WebGLRenderingContext,
    matrix: number[],
    thickness: number,
    position: number,
    camera?: number[]
  ): void {
    gl.useProgram(this.program)
    
    // 设置 uniform 变量
    gl.uniformMatrix4fv(this.uMatrix, false, matrix)
    gl.uniform1f(this.uThickness, thickness)
    gl.uniform1f(this.uDist, position)
    
    if (camera) {
      gl.uniform4fv(this.uCamera, camera)
    }
    
    // 启用顶点属性
    gl.enableVertexAttribArray(this.aPos)
    gl.enableVertexAttribArray(this.aDist)
    
    // 绑定顶点数据
    gl.bindBuffer(gl.ARRAY_BUFFER, this.buffer)
    gl.vertexAttribPointer(this.aPos, 4, gl.FLOAT, false, 0, 0)
    
    // 绑定距离数据
    gl.bindBuffer(gl.ARRAY_BUFFER, this.distBuffer)
    gl.vertexAttribPointer(this.aDist, 1, gl.FLOAT, false, 0, 0)
  }

  /**
   * 渲染方法（子类实现）
   */
  render(
    gl: WebGLRenderingContext,
    matrix: number[],
    thickness: number,
    position: number,
    camera?: number[]
  ): void {
    throw new Error('子类必须实现 render 方法')
  }

  /**
   * 清理资源
   */
  dispose(gl: WebGLRenderingContext): void {
    if (this.program) {
      gl.deleteProgram(this.program)
    }
    if (this.buffer) {
      gl.deleteBuffer(this.buffer)
    }
    if (this.distBuffer) {
      gl.deleteBuffer(this.distBuffer)
    }
  }
}
