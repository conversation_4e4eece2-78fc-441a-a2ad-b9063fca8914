import { Renderer } from './Renderer'

/**
 * 线条渲染器
 * 参照 maps.suunto.com 的 LineRenderer 实现
 */
export class LineRenderer extends Renderer {
  constructor(gl: WebGLRenderingContext) {
    // 完全按照原项目的顶点着色器实现
    const vertexShader = `
      precision highp float;

      uniform mat4 uMatrix;
      uniform float uThickness;
      uniform vec4 uCamera;
      uniform vec2 uOffset;
      attribute vec4 aPos;
      attribute float aDist;
      varying float vItem;
      varying float vAlpha;
      varying float vDist;

      const vec3 alt = vec3(0.0, 0.0, 0.0000002);

      void main() {
        vec3 pos = aPos.xyz;

        float item = aPos.w;
        float side = -1.0;

        if(item < 3.0) {
          item -= 2.0;
        } else {
          side = 1.0;
          item -= 3.0;
        }

        vItem = item;
        vDist = aDist;

        pos += alt;
        pos += normalize(uCamera.xyz - pos) * 0.000001;

        vec4 xyPos = uMatrix * vec4(pos, 1.0);

        xyPos += vec4(uOffset, 0.0, 0.0) * side * xyPos.w * uThickness;
        vAlpha = 1.0 - smoothstep(max(5.0, uCamera.w), max(7.5, uCamera.w * 2.0), length(uCamera.xyz - pos) / uCamera.z);

        gl_Position = xyPos;
      }
    `

    // 完全按照原项目的片段着色器实现
    const fragmentShader = `
      precision highp float;

      uniform float uAlpha;
      uniform float uDist;
      varying float vItem;
      varying float vAlpha;
      varying float vDist;

      void main() {
        if(vDist > uDist) discard;
        if(vItem == 1.0) {
          gl_FragColor = vec4(1.0, 1.0, 1.0, 1.0) * uAlpha * vAlpha;
        } else {
          gl_FragColor = vec4(1.0, 1.0 - vItem, 0.0, 1.0) * uAlpha * vAlpha;
        }
      }
    `

    super(gl, vertexShader, fragmentShader)
  }

  /**
   * 渲染线条
   * 参照原项目的多重渲染策略
   */
  render(
    gl: WebGLRenderingContext,
    matrix: number[],
    thickness: number,
    position: number,
    camera?: number[]
  ): void {
    const count = this.count
    if (count === 0) return

    // 设置基础渲染状态
    this.setupRenderState(gl, matrix, thickness, position, camera)

    // 完全按照原项目的多重绘制策略
    gl.uniform1f(this.uAlpha, 1.0)
    gl.uniform1f(this.uDist, position)

    // 第一次绘制：对角线偏移
    gl.uniform2f(this.uOffset, Math.SQRT2 / 2, Math.SQRT2 / 2)
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, count * 2)

    // 第二次绘制：另一个对角线偏移
    gl.uniform2f(this.uOffset, Math.SQRT2 / 2, -Math.SQRT2 / 2)
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, count * 2)

    // 第三次绘制：水平偏移
    gl.uniform2f(this.uOffset, 1, 0)
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, count * 2)

    // 第四次绘制：垂直偏移
    gl.uniform2f(this.uOffset, 0, 1)
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, count * 2)

    // 第五次绘制：阴影效果（禁用深度测试，降低透明度）
    gl.disable(gl.DEPTH_TEST)
    gl.uniform1f(this.uAlpha, 0.125)
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, count * 2)
    gl.enable(gl.DEPTH_TEST)

    // 调试日志
    if (Math.random() < 0.01) {
      console.log('LineRenderer 绘制:', {
        count,
        vertices: count * 2,
        thickness,
        position
      })
    }

    // 恢复混合状态
    gl.disable(gl.BLEND)
  }
}
