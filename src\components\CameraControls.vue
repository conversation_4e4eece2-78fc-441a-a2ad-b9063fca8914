<template>
  <div class="camera-controls">
    <n-card size="small" class="controls-card">
      <!-- 播放控制 -->
      <div class="playback-controls">
        <n-button-group>
          <n-button @click="handleReset" size="small" quaternary>
            <template #icon>
              <n-icon><SkipBackIcon /></n-icon>
            </template>
          </n-button>
          
          <n-button @click="handleTogglePlay" size="small" type="primary">
            <template #icon>
              <n-icon>
                <PlayIcon v-if="!isPlaying" />
                <PauseIcon v-else />
              </n-icon>
            </template>
          </n-button>
          
          <n-button @click="handleSkipForward" size="small" quaternary>
            <template #icon>
              <n-icon><SkipForwardIcon /></n-icon>
            </template>
          </n-button>
        </n-button-group>
      </div>

      <!-- 进度条 -->
      <div class="progress-control">
        <n-slider
          v-model:value="localPosition"
          :min="0"
          :max="1"
          :step="0.001"
          @update:value="handlePositionChange"
          :tooltip="false"
        />
        <div class="progress-info">
          <span class="current-time">{{ formatTime(currentTime) }}</span>
          <span class="total-time">{{ formatTime(totalTime) }}</span>
        </div>
      </div>

      <!-- 速度控制 -->
      <div class="speed-control">
        <n-space align="center" size="small">
          <n-icon size="16"><SpeedIcon /></n-icon>
          <n-select
            v-model:value="localSpeed"
            :options="speedOptions"
            size="small"
            style="width: 80px"
            @update:value="handleSpeedChange"
          />
          <span class="speed-label">{{ t('controls.speed') }}</span>
        </n-space>
      </div>

      <!-- 相机模式切换 -->
      <div class="camera-mode">
        <n-radio-group v-model:value="cameraMode" size="small">
          <n-radio-button value="follow">
            {{ t('controls.follow') }}
          </n-radio-button>
          <n-radio-button value="free">
            {{ t('controls.free') }}
          </n-radio-button>
        </n-radio-group>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import {
  NCard,
  NButton,
  NButtonGroup,
  NSlider,
  NSelect,
  NSpace,
  NIcon,
  NRadioGroup,
  NRadioButton
} from 'naive-ui'
import {
  Play as PlayIcon,
  Pause as PauseIcon,
  PlayBack as SkipBackIcon,
  PlayForward as SkipForwardIcon,
  Flash as SpeedIcon
} from '@vicons/ionicons5'

// Props
interface Props {
  position: number
  isPlaying: boolean
  speed: number
  duration?: number
}

const props = withDefaults(defineProps<Props>(), {
  duration: 60000 // 默认60秒
})

// Emits
const emit = defineEmits<{
  positionChange: [position: number]
  togglePlay: []
  speedChange: [speed: number]
  reset: []
  cameraMode: [mode: 'follow' | 'free']
}>()

// 国际化
const { t } = useI18n()

// 本地状态
const localPosition = ref(props.position)
const localSpeed = ref(props.speed)
const cameraMode = ref<'follow' | 'free'>('follow')

// 速度选项
const speedOptions = [
  { label: '0.25x', value: 0.25 },
  { label: '0.5x', value: 0.5 },
  { label: '0.75x', value: 0.75 },
  { label: '1x', value: 1 },
  { label: '1.25x', value: 1.25 },
  { label: '1.5x', value: 1.5 },
  { label: '2x', value: 2 },
  { label: '3x', value: 3 },
  { label: '5x', value: 5 }
]

// 计算属性
const currentTime = computed(() => props.position * props.duration)
const totalTime = computed(() => props.duration)

// 格式化时间
const formatTime = (milliseconds: number): string => {
  const totalSeconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(totalSeconds / 60)
  const seconds = totalSeconds % 60
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

// 事件处理
const handlePositionChange = (value: number) => {
  emit('positionChange', value)
}

const handleTogglePlay = () => {
  emit('togglePlay')
}

const handleSpeedChange = (value: number) => {
  emit('speedChange', value)
}

const handleReset = () => {
  emit('reset')
}

const handleSkipForward = () => {
  const newPosition = Math.min(props.position + 0.1, 1)
  emit('positionChange', newPosition)
}

// 监听props变化，同步本地状态
watch(
  () => props.position,
  (newPosition) => {
    localPosition.value = newPosition
  }
)

watch(
  () => props.speed,
  (newSpeed) => {
    localSpeed.value = newSpeed
  }
)

watch(
  () => cameraMode.value,
  (newMode) => {
    emit('cameraMode', newMode)
  }
)
</script>

<style scoped>
.camera-controls {
  user-select: none;
}

.controls-card {
  min-width: 400px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
}

.controls-card :deep(.n-card__content) {
  padding: 16px;
}

.playback-controls {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.progress-control {
  margin-bottom: 16px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--n-text-color-2);
  margin-top: 4px;
}

.speed-control {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.speed-label {
  font-size: 12px;
  color: var(--n-text-color-2);
}

.camera-mode {
  display: flex;
  justify-content: center;
}

/* 深色主题适配 */
.dark .controls-card {
  background: rgba(0, 0, 0, 0.8);
}
</style>
